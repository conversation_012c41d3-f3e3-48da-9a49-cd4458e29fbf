/*
 * Copyright (c) 2021 伦图科技（长沙）有限公司. All rights reserved.
 */

package com.logictrue.interfaces.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.logictrue.interfaces.domain.ExecuteEntity;
import com.logictrue.interfaces.domain.MagicApiFile;
import com.logictrue.interfaces.domain.dto.ApiList;
import com.logictrue.interfaces.domain.vo.MagicInterfacesBodyVO;

import java.util.List;
import java.util.Map;

/**
 * interfacesService接口
 *
 * <AUTHOR>
 * @date 2021-12-01
 */
public interface IMagicApiFileService extends IService<MagicApiFile> {
    List<MagicInterfacesBodyVO> treeInterfaces();

    Map<String, String> interfacesUriById(String id);

    List<ApiList> interfaceTree();

    List<JSONObject> datasources();

    Object execute(ExecuteEntity entity);

    void reloadMagicApiData();
}
