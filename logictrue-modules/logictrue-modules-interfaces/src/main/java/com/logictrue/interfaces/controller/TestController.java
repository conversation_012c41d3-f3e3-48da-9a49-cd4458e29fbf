package com.logictrue.interfaces.controller;


import com.logictrue.common.core.domain.R;
import com.logictrue.common.security.service.TokenService;
import com.logictrue.interfaces.service.IMagicApiFileService;
import com.logictrue.system.api.RemoteUserService;
import com.logictrue.system.api.domain.SysUser;
import com.logictrue.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {


    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMagicApiFileService magicApiFileService;


    @GetMapping("/getuser")
    public LoginUser user() {
        return tokenService.getLoginUser();
    }

    @GetMapping("/setUser")
    public LoginUser setuser() {
        R<LoginUser> setCommitUserId = remoteUserService.userIdInfo("1");
        if (R.verify(setCommitUserId)) {
            LoginUser data = setCommitUserId.getData();
            data.setUserid(data.getSysUser().getUserId());
            tokenService.setTemporaryUserInfo(setCommitUserId.getData());
        } else {
        }
        return tokenService.getLoginUser();
    }

    @GetMapping("/reloadMagicApi")
    public R<String> reloadMagicApi() {
        try {
            magicApiFileService.reloadMagicApiData();
            return R.ok("Magic API data reloaded successfully");
        } catch (Exception e) {
            return R.fail("Failed to reload Magic API data: " + e.getMessage());
        }
    }

}
