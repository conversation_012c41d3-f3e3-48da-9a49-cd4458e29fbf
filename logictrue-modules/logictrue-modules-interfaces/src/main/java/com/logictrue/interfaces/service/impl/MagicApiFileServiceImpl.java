/*
 * Copyright (c) 2021 伦图科技（长沙）有限公司. All rights reserved.
 */

package com.logictrue.interfaces.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.common.core.constant.StrConstants;
import com.logictrue.common.datasource.mybatis.Wraps;
import com.logictrue.interfaces.domain.ExecuteEntity;
import com.logictrue.interfaces.domain.MagicApiFile;
import com.logictrue.interfaces.domain.dto.ApiList;
import com.logictrue.interfaces.domain.vo.MagicInterfacesBodyVO;
import com.logictrue.interfaces.mapper.MagicApiFileMapper;
import com.logictrue.interfaces.service.IMagicApiFileService;
import com.logictrue.interfaces.strategy.response.ResponseContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.logictrue.interfaces.interfaces.core.exception.MagicAPIException;
import com.logictrue.interfaces.interfaces.core.model.ApiInfo;
import com.logictrue.interfaces.interfaces.core.model.Group;
import com.logictrue.interfaces.interfaces.core.model.Parameter;
import com.logictrue.interfaces.interfaces.core.model.TreeNode;
import com.logictrue.interfaces.interfaces.core.service.MagicAPIService;
import com.logictrue.interfaces.interfaces.core.service.MagicResourceService;
import com.logictrue.interfaces.interfaces.core.service.impl.DefaultMagicResourceService;
import com.logictrue.interfaces.interfaces.core.service.impl.RequestMagicDynamicRegistry;
import com.logictrue.interfaces.interfaces.utils.PathUtils;
import com.logictrue.interfaces.interfaces.utils.ScriptManager;
import com.logictrue.interfaces.config.ScriptExecutorConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.DisposableBean;
import org.ssssssss.script.MagicScriptContext;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * interfacesService业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-01
 */
@Service
public class MagicApiFileServiceImpl extends ServiceImpl<MagicApiFileMapper, MagicApiFile> implements IMagicApiFileService, DisposableBean {

    @Autowired
    private MagicAPIService magicAPIService;

    @Autowired
    private MagicResourceService magicResourceService;

    @Autowired
    private RequestMagicDynamicRegistry requestMagicDynamicRegistry;

    @Autowired
    private ScriptExecutorConfig scriptExecutorConfig;

    @Autowired
    @Qualifier("scriptExecutor")
    private Executor scriptExecutor;


    /**
     * <p>
     * 获取接口结构树
     * </p>
     */
    public List<MagicInterfacesBodyVO> treeInterfaces() {
        // 所有可用接口
        List<MagicApiFile> magicApiFiles = this.baseMapper.selectList(Wraps.<MagicApiFile>lbQ()
                .like(MagicApiFile::getFilePath, ".ms"));
        if (CollectionUtils.isEmpty(magicApiFiles)) {
            return Collections.EMPTY_LIST;
        }

        // 用于返回的接口对象列表
        List<MagicInterfacesBodyVO> result = new ArrayList<>();
        // 处理查询的数据
        magicApiFiles.stream().forEach(e -> {
            // 由于数据库存储的json串不是标准的, 下方还有由================================ 分割的脚本 所有这里切割开来
            String[] split = e.getFileContent().split("================================");
            if (split.length == 2) {
                JSONObject interfacesInfo = JSON.parseObject(split[0]);
                MagicInterfacesBodyVO build = MagicInterfacesBodyVO.builder().build();
                build.setInterfaceId(interfacesInfo.getString("id"))
                        .setName(interfacesInfo.getString("name"))
                        .setRequestBody(interfacesInfo.getString("requestBody"))
                        .setResponseBody(interfacesInfo.getString("responseBody"));
                result.add(build);
            }
        });
        return result;
    }

    @Override
    public List<JSONObject> datasources() {
        List<MagicApiFile> magicApiFiles = this.baseMapper.selectList(Wraps.<MagicApiFile>lbQ().like(MagicApiFile::getFilePath,
                "/datasource/"));
        if (CollectionUtils.isEmpty(magicApiFiles)) {
            return null;
        }
        List<JSONObject> datasources = new ArrayList<>();
        // 将所有目录转换格式先存入返回列表中
        magicApiFiles.stream().filter(file -> !"this is directory".equals(file.getFileContent())).forEach(group -> {
            JSONObject jsonObject = JSON.parseObject(group.getFileContent());
            datasources.add(jsonObject);
        });
        return datasources;
    }


    public void inter(TreeNode<Group> groupTreeNode, List<ApiList> apiListTree) {
        Group node = groupTreeNode.getNode();
        apiListTree.add(ApiList.builder()
                .name(node.getName())
                .parentId(node.getParentId())
                .interfaceType("group")
                .interfaceId(node.getId()).build());
        if (!CollectionUtils.isEmpty(groupTreeNode.getChildren())) {
            groupTreeNode.getChildren().forEach(e -> {
                this.inter(e, apiListTree);
            });
        }
    }

    /**
     * <p>
     * 接口树,将在线接口树以树结构展示
     * </p>
     */
    @Override
    public List<ApiList> interfaceTree() {
        TreeNode<Group> tree = magicResourceService.tree("api");
        if (Objects.isNull(tree)) {
            return null;
        }
        List<ApiList> apiListTree = new ArrayList<>();
        tree.getChildren().forEach(e -> {
            this.inter(e, apiListTree);
        });

        List<ApiInfo> apiInfos = ((DefaultMagicResourceService) magicResourceService).files("api");
        // 将接口根据组id分组
        Map<String, List<ApiInfo>> apiByGroupId = apiInfos.stream().collect(Collectors.groupingBy(e -> e.getGroupId()));
        // 遍历分组接口
        apiByGroupId.forEach((key, value) -> {
            // 是否插入到某一个组数据下
            AtomicReference<Boolean> isInsert = new AtomicReference(Boolean.FALSE);
            // 将对应组的api转换存入组子数据中
            apiListTree.stream().forEach(e -> {
                if (e.getInterfaceId().equals(key)) {
                    // 匹配接口类型数据 属于同一组的设置到组子列表中
                    e.setChildren(value.stream().map(api -> {
                        // 将Get请求参数转换为参数map
                        Map<String, Object> parameterMap = new HashMap<>();
                        for (Parameter parameter : api.getParameters()) {
                            parameterMap.put(parameter.getName(), parameter.getValue());
                        }
                        // 解析返回值
                        List<Object> paramList = new ArrayList<>();

                        List<Object> setBigParamList = new ArrayList<>();
                        String responseBody = null;
                        try {
                            responseBody = api.getResponseBody();
                            if (StringUtils.isNotBlank(responseBody)) {
                                JSONObject respJsonObject = JSONObject.parseObject(responseBody);

                                Object jsonData = respJsonObject.get("data");
//
                                ResponseContext responseContext = new ResponseContext(jsonData);
                                // 给报表接口解析使用
                                paramList = responseContext.getResult();


                                //只解析字段名称 给大屏使用
                                int flag = 0;
                                if (StringUtils.isNotBlank(responseBody) && respJsonObject.containsKey("data")) {
                                    try {
                                        setBigParamList = respJsonObject.getJSONObject("data").keySet().stream().collect(Collectors.toList());
                                    } catch (Exception ae) {
                                    }
                                    if (CollectionUtils.isEmpty(setBigParamList)) {
                                        try {
                                            setBigParamList = respJsonObject.getJSONArray("data").getJSONObject(0).keySet().stream().collect(Collectors.toList());
                                            flag = 1;
                                        } catch (Exception oe) {
                                        }
                                    }
                                    //处理分页数据data
                                    if (flag == 0) {
                                        Object obj = jsonData;
                                        if (obj instanceof JSONObject) {
                                            JSONObject data = (JSONObject) obj;
                                            if (StringUtils.isNotBlank(data.getString("current")) && StringUtils.isNotBlank(data.getString("size")) && org.apache.commons.collections.CollectionUtils.isNotEmpty(data.getJSONArray("records"))) {
                                                setBigParamList = data.getJSONArray("records").getJSONObject(0).keySet().stream().collect(Collectors.toList());
                                            }
                                        }
                                    }
                                }
                            }

                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                        return ApiList.builder()
                                .interfaceType("api")
                                .url(api.getPath())
                                .setParamList(paramList)
                                .setBigParamList(setBigParamList)
                                .parentId(api.getGroupId())
                                .requestBody(api.getRequestBody())
                                .responseBody(responseBody)
                                .parameters(JSON.toJSONString(parameterMap))
                                .methodType(api.getMethod())
                                .interfaceId(api.getId())
//                                .disabled(Boolean.FALSE)
                                .name(api.getName())
                                .build();
                    }).collect(Collectors.toList()));
                    isInsert.set(Boolean.TRUE);
                }
            });
        });
        // 根据父级id进行分组
        Map<String, List<ApiList>> apiListGroupByParentId = apiListTree.stream().collect(Collectors.groupingBy(ApiList::getParentId));

        // 遍历列表将拥有子列表的数据放置到子列表中
        apiListTree.parallelStream().forEach(e -> {
            if (apiListGroupByParentId.containsKey(e.getInterfaceId())) {
                e.setChildren(apiListGroupByParentId.get(e.getInterfaceId()));
            }
        });
        // 返回父级id为0的数据，0 则是顶级菜单
        return apiListTree.stream().filter(e -> StrConstants.ZERO.equals(e.getParentId())).collect(Collectors.toList());
    }


    @Override
    public Map<String, String> interfacesUriById(String id) {

        Map<String, String> results = new HashMap<>();

        List<MagicApiFile> magicApiFiles = this.baseMapper.selectList(Wraps.lbQ());
        if (CollectionUtils.isEmpty(magicApiFiles)) {
            return null;
        }
        // 获取对应Id的接口数据
        List<MagicApiFile> thisIdApis = magicApiFiles.stream().filter(e -> {
            String[] split = e.getFileContent().split("================================");
            e.setFileContent(split[0]);
            return e.getFileContent().contains("\"id\" : \"" + id + "\"");
        }).collect(Collectors.toList());
        // 如果没有找到对应id的接口数据
        if (CollectionUtils.isEmpty(thisIdApis) || thisIdApis.size() != 1) {
            return null;
        }
        // 当前id对应的接口
        MagicApiFile magicApiFile = thisIdApis.get(0);
        // 计算的路径字段
        StringBuffer resultUri = new StringBuffer();
        this.recursion(magicApiFiles, resultUri, magicApiFile.getFilePath());
        JSONObject jsonObject = JSON.parseObject(magicApiFile.getFileContent());

        String path = JSON.parseObject(magicApiFile.getFileContent()).getString("path");
        if (!path.contains("/")) {
            //在线接口设计界面 请求路径未填写斜杠"/"时补全斜杠
            resultUri.append("/").append(path);
        } else {
            resultUri.append(path);
        }

        results.put("url", resultUri.toString());
        results.put("method", jsonObject.getString("method"));
        results.put("name", jsonObject.getString("name"));
        return results;
    }

    // 递归获取接口uri
    public void recursion(List<MagicApiFile> magicApiFiles, StringBuffer stringBuffer, String filePath) {
        String[] split = filePath.split("/");
        // /interfaces/api/测试接口/group.json
        StringBuilder parentUri = new StringBuilder();
        // 去除最后一截的地址
        for (int i = 0; i < split.length; i++) {
            if (i < split.length - 1 && StringUtils.isNotBlank(split[i])) {
                parentUri.append("/").append(split[i]);
            }
        }
        parentUri.append("/").append("group.json");
        MagicApiFile magicApiFile = magicApiFiles.stream()
                .filter(e -> e.getFilePath().equals(parentUri.toString())).findFirst().orElse(null);
        if (Objects.nonNull(magicApiFile)) {
            JSONObject jsonObject = JSON.parseObject(magicApiFile.getFileContent());
            String path = jsonObject.getString("path");
            stringBuffer.insert(0, path);
            if (!path.contains("/")) {
                stringBuffer.insert(0, "/");
            }
            // 不等于零则代表还有父级
            if (!"0".equals(jsonObject.getString("parentId"))) {
                recursion(magicApiFiles, stringBuffer, parentUri.toString().replaceAll("/group.json", ""));
            }
        } else {
            return;
        }
    }

    @Override
    public Object execute(ExecuteEntity entity) {
        ApiInfo info = requestMagicDynamicRegistry.getMapping(entity.getApiId());
        if (info == null) {
            throw new MagicAPIException("找不到对应接口 " + entity.getApiId());
        }

        if (Objects.isNull(entity.getBody())) {
            entity.setBody(new HashMap<>());
        }

        Map<String, Object> context = info.getMethod().equals("POST") ?
                new HashMap<String, Object>() {{
                    put("body", entity.getBody());
                }} : entity.getBody();

        context.put("apiInfo", info);

        String fullGroupName = magicResourceService.getGroupName(info.getGroupId());
        String fullGroupPath = magicResourceService.getGroupPath(info.getGroupId());
        String scriptName = PathUtils.replaceSlash(String.format("/%s/%s(/%s/%s)", fullGroupName, info.getName(), fullGroupPath, info.getPath()));

        // 使用多线程方式执行脚本
        return executeScriptAsync(info.getScript(), scriptName, context);
    }

    /**
     * 异步执行脚本方法
     *
     * @param script 脚本内容
     * @param scriptName 脚本名称
     * @param context 上下文参数
     * @return 执行结果
     */
    private Object executeScriptAsync(String script, String scriptName, Map<String, Object> context) {
        try {
            // 使用CompletableFuture提交任务到线程池执行
            CompletableFuture<Object> future = CompletableFuture.supplyAsync(() -> {
                // 为每个线程创建独立的上下文
                MagicScriptContext scriptContext = new MagicScriptContext();
                scriptContext.setScriptName(scriptName);
                scriptContext.putMapIntoContext(new HashMap<>(context)); // 创建副本避免并发问题

                // 添加线程信息到上下文
                scriptContext.put("threadId", Thread.currentThread().getId());
                scriptContext.put("threadName", Thread.currentThread().getName());
                scriptContext.put("executeTime", System.currentTimeMillis());

                try {
                    return ScriptManager.executeScript(script, scriptContext);
                } catch (Exception e) {
                    throw new RuntimeException("脚本执行失败: " + e.getMessage(), e);
                }
            }, scriptExecutor);

            // 等待执行结果，使用配置的超时时间
            return future.get(scriptExecutorConfig.getTimeoutSeconds(), TimeUnit.SECONDS);

        } catch (TimeoutException e) {
            throw new MagicAPIException("脚本执行超时，请检查脚本逻辑或增加超时时间");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new MagicAPIException("脚本执行被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new MagicAPIException("脚本执行异常: " + cause.getMessage(), cause);
            }
        }
    }

    @Override
    public void reloadMagicApiData() {
        magicResourceService.refresh();
    }

    /**
     * 批量执行脚本方法（支持多个脚本并发执行）
     *
     * @param executeEntities 执行实体列表
     * @return 执行结果列表
     */
    public List<Object> executeBatch(List<ExecuteEntity> executeEntities) {
        if (CollectionUtils.isEmpty(executeEntities)) {
            return Collections.emptyList();
        }

        List<CompletableFuture<Object>> futures = executeEntities.stream()
                .map(entity -> CompletableFuture.supplyAsync(() -> execute(entity), scriptExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        try {
            // 使用配置的批量执行超时时间
            allFutures.get(scriptExecutorConfig.getBatchTimeoutSeconds(), TimeUnit.SECONDS);

            // 收集所有结果
            return futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

        } catch (TimeoutException e) {
            throw new MagicAPIException("批量脚本执行超时");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new MagicAPIException("批量脚本执行被中断");
        } catch (ExecutionException e) {
            throw new MagicAPIException("批量脚本执行异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取线程池状态信息
     *
     * @return 线程池状态
     */
    public Map<String, Object> getExecutorStatus() {
        Map<String, Object> status = new HashMap<>();
        if (scriptExecutor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) scriptExecutor;
            status.put("corePoolSize", executor.getCorePoolSize());
            status.put("maxPoolSize", executor.getMaxPoolSize());
            status.put("activeCount", executor.getActiveCount());
            status.put("poolSize", executor.getPoolSize());
            status.put("queueSize", executor.getThreadPoolExecutor().getQueue().size());
            status.put("completedTaskCount", executor.getThreadPoolExecutor().getCompletedTaskCount());
        }
        return status;
    }

    /**
     * Spring Bean销毁时调用
     */
    @Override
    public void destroy() throws Exception {
        // Spring会自动管理ThreadPoolTaskExecutor的生命周期
        // 这里可以添加其他清理逻辑
    }
}
