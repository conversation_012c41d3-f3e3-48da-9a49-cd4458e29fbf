/*
 * Copyright (c) 2021 伦图科技（长沙）有限公司. All rights reserved.
 */

package com.logictrue.interfaces.controller;

import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.common.core.web.domain.AjaxResult;
import com.logictrue.interfaces.domain.ExecuteEntity;
import com.logictrue.interfaces.service.impl.MagicApiFileServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 脚本执行器控制器
 */
@Api(tags = "脚本执行器管理")
@RestController
@RequestMapping("/scriptExecutor")
public class ScriptExecutorController extends BaseController {

    @Autowired
    private MagicApiFileServiceImpl magicApiFileService;

    /**
     * 批量执行脚本
     */
    @ApiOperation("批量执行脚本")
    @PostMapping("/executeBatch")
    public AjaxResult executeBatch(
            @ApiParam("执行实体列表") @RequestBody List<ExecuteEntity> executeEntities) {
        try {
            List<Object> results = magicApiFileService.executeBatch(executeEntities);
            return AjaxResult.success("批量执行成功", results);
        } catch (Exception e) {
            logger.error("批量执行脚本失败", e);
            return AjaxResult.error("批量执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池状态
     */
    @ApiOperation("获取线程池状态")
    @GetMapping("/status")
    public AjaxResult getExecutorStatus() {
        try {
            Map<String, Object> status = magicApiFileService.getExecutorStatus();
            return AjaxResult.success("获取状态成功", status);
        } catch (Exception e) {
            logger.error("获取线程池状态失败", e);
            return AjaxResult.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 单个脚本执行（支持多线程）
     */
    @ApiOperation("执行单个脚本")
    @PostMapping("/execute")
    public AjaxResult execute(
            @ApiParam("执行实体") @RequestBody ExecuteEntity executeEntity) {
        try {
            Object result = magicApiFileService.execute(executeEntity);
            return AjaxResult.success("执行成功", result);
        } catch (Exception e) {
            logger.error("执行脚本失败", e);
            return AjaxResult.error("执行失败: " + e.getMessage());
        }
    }
}
